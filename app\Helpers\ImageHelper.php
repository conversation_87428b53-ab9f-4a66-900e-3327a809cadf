<?php

if (!function_exists('optimizedImage')) {
    /**
     * Generate optimized image HTML with lazy loading
     */
    function optimizedImage($imagePath, $alt = '', $class = '', $width = null, $height = null)
    {
        $fullPath = asset('assets/img/' . $imagePath);
        
        // Add lazy loading and responsive attributes
        $attributes = [
            'src' => $fullPath,
            'alt' => $alt,
            'class' => $class,
            'loading' => 'lazy',
            'decoding' => 'async'
        ];
        
        if ($width) {
            $attributes['width'] = $width;
        }
        
        if ($height) {
            $attributes['height'] = $height;
        }
        
        $attributeString = '';
        foreach ($attributes as $key => $value) {
            if ($value) {
                $attributeString .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
            }
        }
        
        return '<img' . $attributeString . '>';
    }
}

if (!function_exists('responsiveImage')) {
    /**
     * Generate responsive image with WebP support
     */
    function responsiveImage($imagePath, $alt = '', $class = '', $sizes = '100vw')
    {
        $basePath = 'assets/img/' . $imagePath;
        $pathInfo = pathinfo($imagePath);
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'];
        $directory = isset($pathInfo['dirname']) && $pathInfo['dirname'] !== '.' ? $pathInfo['dirname'] . '/' : '';
        
        // Generate different sizes
        $srcset = [];
        $webpSrcset = [];
        
        $sizes = [400, 600, 800, 1200];
        foreach ($sizes as $size) {
            $resizedPath = 'assets/img/optimized/' . $directory . $filename . '_' . $size . 'w.' . $extension;
            $webpPath = 'assets/img/optimized/' . $directory . $filename . '_' . $size . 'w.webp';
            
            $srcset[] = asset($resizedPath) . ' ' . $size . 'w';
            $webpSrcset[] = asset($webpPath) . ' ' . $size . 'w';
        }
        
        $html = '<picture>';
        
        // WebP source
        if (!empty($webpSrcset)) {
            $html .= '<source srcset="' . implode(', ', $webpSrcset) . '" sizes="' . $sizes . '" type="image/webp">';
        }
        
        // Fallback source
        if (!empty($srcset)) {
            $html .= '<source srcset="' . implode(', ', $srcset) . '" sizes="' . $sizes . '">';
        }
        
        // Default img tag
        $html .= '<img src="' . asset($basePath) . '" alt="' . htmlspecialchars($alt) . '" class="' . htmlspecialchars($class) . '" loading="lazy" decoding="async">';
        $html .= '</picture>';
        
        return $html;
    }
}

if (!function_exists('criticalImage')) {
    /**
     * Generate image for above-the-fold content (no lazy loading)
     */
    function criticalImage($imagePath, $alt = '', $class = '', $width = null, $height = null)
    {
        $fullPath = asset('assets/img/' . $imagePath);
        
        $attributes = [
            'src' => $fullPath,
            'alt' => $alt,
            'class' => $class,
            'decoding' => 'async'
        ];
        
        if ($width) {
            $attributes['width'] = $width;
        }
        
        if ($height) {
            $attributes['height'] = $height;
        }
        
        $attributeString = '';
        foreach ($attributes as $key => $value) {
            if ($value) {
                $attributeString .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
            }
        }
        
        return '<img' . $attributeString . '>';
    }
}
