<?php

namespace App\Http\Controllers\FrontEnd;

use App\Http\Controllers\Controller;
use App\Http\Controllers\FrontEnd\MiscellaneousController;
use App\Models\BasicSettings\Basic;
use App\Models\BasicSettings\BasicExtends;
use App\Models\Blog\Post;
use App\Models\ClientService\ServiceContent;
use App\Models\HomePage\CtaSectionInfo;
use App\Models\HomePage\Partner;
use App\Models\HomePage\Section;
use App\Models\Package;
use App\Services\HomePageCacheService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class HomeController extends Controller
{
  protected $cacheService;

  public function __construct(HomePageCacheService $cacheService)
  {
    $this->cacheService = $cacheService;
  }

  public function index(Request $request)
  {
    $themeVersion = Basic::query()->pluck('theme_version')->first();

    $misc = new MiscellaneousController();
    $language = $misc->getLanguage();

    // Use cached data for better performance
    $queryResult = $this->cacheService->getHomePageData($language, $themeVersion);

    // Add non-cached data
    $queryResult['languageId'] = $language;
    $queryResult['seoInfo'] = $language->seoInfo()->select('meta_title_home', 'meta_keyword_home', 'meta_description_home')->first();

    // Optimized: Single query for all basic settings
    $basicSettings = Basic::query()->select(
      'hero_static_img',
      'hero_video_url',
      'hero_bg_img',
      'about_section_image',
      'about_section_video_link',
      'feature_bg_img',
      'cta_bg_img'
    )->first();

    if ($themeVersion == 1 || $themeVersion == 2 || $themeVersion == 3) {
      $queryResult['heroImg'] = $basicSettings->hero_static_img;
      $queryResult['heroVideoUrl'] = $basicSettings->hero_video_url;
      $queryResult['heroInfo'] = $language->heroStatic()->first();
      $queryResult['heroBgImg'] = $basicSettings->hero_bg_img;
    } else {
      $queryResult['heroInfo'] = $language->heroStatic()->first();
    }

    $queryResult['categories'] = $language->serviceCategory()->where('status', 1)->orderBy('serial_number', 'asc')->limit(8)->get();

    if ($secInfo->about_section_status == 1) {
      $queryResult['aboutInfo'] = (object)[
        'about_section_image' => $basicSettings->about_section_image,
        'about_section_video_link' => $basicSettings->about_section_video_link
      ];
      $queryResult['aboutData'] = $language->aboutSection()->first();
    }

    $queryResult['secTitle'] = $language->sectionTitle()->first();

    if ($secInfo->features_section_status == 1) {
      $queryResult['featureBgImg'] = $basicSettings->feature_bg_img;
      $queryResult['allFeature'] = $language->feature()->orderByDesc('id')->get();
    }
    $service_setings = Basic::select('is_service')->first();
    $queryResult['service_setings'] = $service_setings;
    if ($secInfo->featured_services_section_status == 1 && $service_setings->is_service == 1) {
      // EMERGENCY FIX: Simplified query to reduce load time
      $categories = $language->serviceCategory()
        ->where('status', 1)
        ->where('is_featured', 'yes')
        ->orderBy('serial_number', 'asc')
        ->limit(6) // Limit to 6 categories for faster loading
        ->get();

      // Get service contents with a single optimized query
      foreach ($categories as $category) {
        $category['serviceContent'] = ServiceContent::query()
          ->join('services', 'service_contents.service_id', '=', 'services.id')
          ->where('service_contents.service_category_id', $category->id)
          ->where('services.service_status', 1)
          ->where('services.is_featured', 'yes')
          ->select('service_contents.*')
          ->limit(3) // Limit services per category
          ->get();
      }

      $queryResult['featuredCategories'] = $categories;
    }

    $queryResult['currencyInfo'] = $this->getCurrencyInfo();

    if ($secInfo->testimonials_section_status == 1) {
      $queryResult['testimonialBgImg'] = Basic::query()->pluck('testimonial_bg_img')->first();
    }
    $queryResult['testimonials'] = $language->testimonial()->orderByDesc('id')->get();

    if ($secInfo->blog_section_status == 1) {
      $queryResult['posts'] = Post::query()->join('post_informations', 'posts.id', '=', 'post_informations.post_id')
        ->join('blog_categories', 'blog_categories.id', '=', 'post_informations.blog_category_id')
        ->where('post_informations.language_id', '=', $language->id)
        ->select('posts.id', 'posts.image', 'blog_categories.name as categoryName', 'blog_categories.slug as categorySlug', 'post_informations.title', 'post_informations.slug', 'post_informations.author', 'post_informations.content', 'posts.created_at')
        ->orderBy('posts.created_at', 'desc')
        ->limit(3)
        ->get();
    }

    if ($secInfo->partners_section_status == 1) {
      $queryResult['partners'] = Partner::query()->orderByDesc('id')->get();
    }

    if ($secInfo->cta_section_status == 1) {
      $queryResult['ctaSectionInfo'] = CtaSectionInfo::where('language_id', $language->id)->first();
      $queryResult['ctaBgImg'] = $basicSettings->cta_bg_img;
    }
    $queryResult['BasicExtends'] = BasicExtends::where('language_id', $language->id)->first();

    if ($themeVersion == 1) {
      return view('frontend.home.index-v1', $queryResult);
    } else if ($themeVersion == 2) {
      return view('frontend.home.index-v2', $queryResult);
    } else if ($themeVersion == 3) {
      return view('frontend.home.index-v3', $queryResult);
    }
  }

  public function pricing()
  {
    $misc = new MiscellaneousController();

    $language = $misc->getLanguage();

    $queryResult['seoInfo'] = $language->seoInfo()->select('pricing_page_meta_title', 'pricing_page_meta_keywords', 'pricing_page_meta_description')->first();

    $queryResult['pageHeading'] = $misc->getPageHeading($language);

    $queryResult['breadcrumb'] = $misc->getBreadcrumb();
    $queryResult['monthly_packages'] = Package::where([['status', '1'], ['term', 'monthly']])->get();
    $queryResult['yearly_packages'] = Package::where([['status', '1'], ['term', 'yearly']])->get();
    $queryResult['lifetime_packages'] = Package::where([['status', '1'], ['term', 'lifetime']])->where('id', '<>', 999999)->get();
    return view('frontend.pricing', $queryResult);
  }
}
