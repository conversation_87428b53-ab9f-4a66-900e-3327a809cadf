<!-- Preload critical resources -->
<link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;600&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;600&display=swap"></noscript>

<!-- DNS prefetch for external resources -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.gstatic.com">

<!-- Critical CSS inline (above-the-fold styles) -->
<style>
/* Critical CSS for initial page load */
body { 
  font-family: 'Inter', sans-serif; 
  margin: 0; 
  padding: 0; 
}
.preloader { 
  position: fixed; 
  top: 0; 
  left: 0; 
  width: 100%; 
  height: 100%; 
  background: #fff; 
  z-index: 9999; 
}
.hero-banner { 
  min-height: 500px; 
  background-size: cover; 
  background-position: center; 
}
.container { 
  max-width: 1200px; 
  margin: 0 auto; 
  padding: 0 15px; 
}
</style>

<!-- Bundled CSS - Load asynchronously -->
<link rel="preload" href="{{ mix('assets/dist/css/frontend-v3.min.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="{{ mix('assets/dist/css/frontend-v3.min.css') }}"></noscript>

<!-- Conditional CSS for inner pages -->
@if (!request()->routeIs('index'))
  <link rel="preload" href="{{ mix('assets/dist/css/innerpages.min.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="{{ mix('assets/dist/css/innerpages.min.css') }}"></noscript>
@endif

<!-- RTL CSS -->
@if ($currentLanguageInfo->direction == 1)
  <link rel="preload" href="{{ mix('assets/dist/css/rtl.min.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="{{ mix('assets/dist/css/rtl.min.css') }}"></noscript>
@endif

<!-- Responsive CSS -->
<link rel="stylesheet" href="{{ asset('assets/front/css/responsive.css') }}">

<!-- CSS loading fallback script -->
<script>
/*! loadCSS. [c]2017 Filament Group, Inc. MIT License */
!function(a){"use strict";var b=function(b,c,d){function e(a){return h.body?a():void setTimeout(function(){e(a)})}function f(){i.addEventListener&&i.removeEventListener("load",f),i.media=d||"all"}var g,h=a.document,i=h.createElement("link");if(c)g=c;else{var j=(h.body||h.getElementsByTagName("head")[0]).childNodes;g=j[j.length-1]}var k=h.styleSheets;i.rel="stylesheet",i.href=b,i.media="only x",e(function(){g.parentNode.insertBefore(i,c?g:g.nextSibling)});var l=function(a){for(var b=i.href,c=k.length;c--;)if(k[c].href===b)return a();setTimeout(function(){l(a)})};return i.addEventListener&&i.addEventListener("load",f),i.onloadcssdefined=l,l(f),i};"undefined"!=typeof exports?exports.loadCSS=b:a.loadCSS=b}("undefined"!=typeof global?global:this);
</script>
