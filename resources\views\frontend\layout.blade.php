<!DOCTYPE html>
<html lang="{{ $currentLanguageInfo->code }}" @if ($currentLanguageInfo->direction == 1) dir="rtl" @endif>
<head>
  {{-- csrf-token for ajax request --}}
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="title" content="@yield('metaTitle')">
  <meta name="keywords" content="@yield('metaKeywords')">
  <meta name="description" content="@yield('metaDescription')">

     @yield('code') 
<?php

$category = isset($_GET['category']) ? $_GET['category'] : '';
$subcategory = isset($_GET['subcategory']) ? $_GET['subcategory'] : '';
//category check
if($category != '' && $subcategory == ''){
  if($category == 'admin-support'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?category=admin-support" />';
  }else if($category == 'seo--marketing'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?category=seo--marketing" />';
  }
  else if($category == 'motion-and-graphics'){
    echo '<link rel="canonical" href="https://brownsofts.com/blog?title=&category=motion-and-graphics" />';
  }
   else if($category == 'digital-marketing'){
    echo '<link rel="canonical" href="https://brownsofts.com/blog" />';
  }
   else if($category == 'video--animation'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?category=video--animation" />';
  }
}

//sub category check
if($category == '' && $subcategory != ''){
  if($subcategory == 'seo'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=seo" />';
  }
  else if($subcategory == 'website-design-services'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=website-design-services" />';
  }
  else if($subcategory == 'website-management'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=website-management" />';
  }
}

//both category and subcategory
if($category != '' && $subcategory != ''){
  if($category == 'video--animation' && $subcategory == 'color-grading--animation'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=color-grading--animation" />';
  } else if($category == 'seo--marketing' && $subcategory == 'seo'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=seo" />'; 
  }
  else if($category == 'video--animation' && $subcategory == 'color-grading--animation'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=color-grading--animation" />'; 
  }
  else if($category == 'web-design' && $subcategory == 'website-design-services'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=website-design-services" />'; 
  }
  else if($category == 'graphics--design' && $subcategory == 'ui--ux-designing'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=ui--ux-designing" />'; 
  }
  else if($category == 'video--animation' && $subcategory == 'events--celebrations'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=events--celebrations" />'; 
  }
  else if($category == 'admin-support' && $subcategory == 'website-management'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?subcategory=website-management" />'; 
  }
}

$admin = isset($_GET['admin']) ? $_GET['admin'] : '';

// Check if the `admin` query parameter is present
if($admin == 1){
    echo '<link rel="canonical" href="https://brownsofts.com/seller/admin?admin=1" />';
}
$keyword = isset($_GET['keyword']) ? $_GET['keyword'] : '';

// Check if the `keyword` is "Admin Support" and output the corresponding canonical URL
if($keyword == 'Admin Support'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?category=admin-support" />';
}
$currentPath = $_SERVER['REQUEST_URI'];

// Check if the path matches '/seller/forget-password'
if (strpos($currentPath, '/seller/forget-password') !== false) {
    echo '<link rel="canonical" href="https://brownsofts.com/user/forget-password" />';
}
$page = isset($_GET['page']) ? $_GET['page'] : '';

// Check if the `page` parameter is set to `5`
if($page == '5'){
    echo '<link rel="canonical" href="https://brownsofts.com/services?category=video--animation" />';
}
?>
  {{-- title --}}
  <title>@yield('pageHeading') {{ '| ' . $websiteInfo->website_title }}</title>
  <!-- Favicon -->
  <link rel="shortcut icon" href="{{ asset('assets/img/' . $websiteInfo->favicon) }}" type="image/x-icon">

  {{-- Critical CSS inline --}}
  <style>
    /* Critical above-the-fold CSS */
    body{font-family:Inter,sans-serif;margin:0;padding:0;line-height:1.6}
    .container{max-width:1200px;margin:0 auto;padding:0 15px}
    .hero-banner{min-height:500px;background-size:cover;background-position:center;display:flex;align-items:center}
    .preloader{position:fixed;top:0;left:0;width:100%;height:100%;background:#fff;z-index:9999;display:flex;align-items:center;justify-content:center}
    .loader{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;animation:spin 1s linear infinite}
    @keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
    .btn{display:inline-block;padding:12px 24px;background:#007bff;color:#fff;text-decoration:none;border-radius:4px;border:none;cursor:pointer}
    .text-center{text-align:center}
    .mb-3{margin-bottom:1rem}
    .d-none{display:none}
  </style>

  {{-- Load non-critical CSS asynchronously --}}
  @if ($basicInfo->theme_version == 1)
    <link rel="preload" href="{{ asset('assets/front/css/style.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="{{ asset('assets/front/css/style.css') }}"></noscript>
  @elseif ($basicInfo->theme_version == 2)
    <link rel="preload" href="{{ asset('assets/front/css/style.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="{{ asset('assets/front/css/style.css') }}"></noscript>
  @elseif ($basicInfo->theme_version == 3)
    <link rel="preload" href="{{ asset('assets/front/css/style.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="{{ asset('assets/front/css/style.css') }}"></noscript>
  @endif

  {{-- Load other CSS files asynchronously --}}
  <link rel="preload" href="{{ asset('assets/front/css/vendors/bootstrap.min.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="{{ asset('assets/front/fonts/fontawesome/css/all.min.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript>
    <link rel="stylesheet" href="{{ asset('assets/front/css/vendors/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/front/fonts/fontawesome/css/all.min.css') }}">
  </noscript>
  @php
    $primaryColor = $basicInfo->primary_color;
  @endphp
  <style>
    :root {
      --color-primary: #{{ $primaryColor }};
      --color-primary-rgb: {{ hexToRgb($primaryColor) }};
    }

    .breadcrumbs-area::after {
      background-color: #{{ $basicInfo->breadcrumb_overlay_color }};
      opacity: {{ $basicInfo->breadcrumb_overlay_opacity }};
    }
  </style>

  {{-- Preconnect to external domains --}}
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://www.googletagmanager.com">

  {{-- Load Google Fonts asynchronously --}}
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;600&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;600&display=swap"></noscript>

  {{-- Defer Google Analytics --}}
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-8JDTCZB860');
    gtag('config', 'G-ZER41DMGQC');

    // Load GA script after page load
    window.addEventListener('load', function() {
      var script = document.createElement('script');
      script.async = true;
      script.src = 'https://www.googletagmanager.com/gtag/js?id=G-8JDTCZB860';
      document.head.appendChild(script);
    });
  </script>

</head>

<body>

  <!-- Preloader start -->
  <div id="preLoader">
    <div class="loader"></div>
  </div>
  <!-- Preloader end -->
  <div class="request-loader">
    <div class="loader-inner">
      <span class="loader"></span>
    </div>
  </div>

  <div class="main-wrapper">

    <!-- Header-area start -->
    @if ($basicInfo->theme_version == 1)
      @includeIf('frontend.partials.header.header-nav-v1')
    @elseif ($basicInfo->theme_version == 2)
      @includeIf('frontend.partials.header.header-nav-v2')
    @elseif ($basicInfo->theme_version == 3)
      @includeIf('frontend.partials.header.header-nav-v3')
    @endif
    <!-- Header-area end -->
    @yield('content')
  </div>

  {{-- announcement popup --}}
  @includeIf('frontend.partials.popups')

  {{-- cookie alert --}}
  @if (!is_null($cookieAlertInfo) && $cookieAlertInfo->cookie_alert_status == 1)
    @includeIf('cookie-consent::index')
  @endif
  {{-- floating whatsapp button --}}
  @if ($basicInfo->whatsapp_status == 1)
    <div class="whatsapp-btn"></div>
  @endif

  <!-- Footer-area start -->
  @if ($basicInfo->theme_version == 1)
    @includeIf('frontend.partials.footer.footer-v1')
  @elseif ($basicInfo->theme_version == 2)
    @includeIf('frontend.partials.footer.footer-v2')
  @elseif ($basicInfo->theme_version == 3)
    @includeIf('frontend.partials.footer.footer-v3')
  @endif
  <!-- Footer-area end-->

  {{-- Load critical JS inline --}}
  <script>
    // Critical JavaScript for immediate functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Hide preloader
      var preloader = document.getElementById('preLoader');
      if (preloader) {
        setTimeout(function() {
          preloader.style.display = 'none';
        }, 500);
      }
    });
  </script>

  {{-- Load non-critical JS after page load --}}
  <script>
    window.addEventListener('load', function() {
      // Load jQuery and other scripts after page load
      var scripts = [
        '{{ asset("assets/js/jquery-3.7.1.min.js") }}',
        '{{ asset("assets/js/bootstrap.min.js") }}',
        '{{ asset("assets/js/popper.min.js") }}'
      ];

      scripts.forEach(function(src, index) {
        setTimeout(function() {
          var script = document.createElement('script');
          script.src = src;
          script.async = true;
          document.head.appendChild(script);
        }, index * 100);
      });
    });
  </script>

  {{-- Theme-specific scripts loaded asynchronously --}}
  @if ($basicInfo->theme_version == 3)
    <script async src="{{ asset('assets/front/js/vendors/aos.min.js') }}"></script>
    <script async src="{{ asset('assets/front/js/vendors/swiper-bundle.min.js') }}"></script>
  @endif
  {{-- additional script --}}
</body>

</html>
