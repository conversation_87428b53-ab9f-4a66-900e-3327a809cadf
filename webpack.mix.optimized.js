const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management - Optimized Version
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. This optimized version combines multiple
 | CSS and JS files to reduce HTTP requests and improve performance.
 |
 */

// Frontend CSS Bundle - Theme V3 (combine all CSS files)
mix.combine([
    'public/assets/front/css/vendors/bootstrap.min.css',
    'public/assets/front/fonts/fontawesome/css/all.min.css',
    'public/assets/front/fonts/icomoon/style.css',
    'public/assets/front/css/vendors/magnific-popup.min.css',
    'public/assets/front/css/vendors/swiper-bundle.min.css',
    'public/assets/front/css/vendors/nice-select.css',
    'public/assets/front/css/vendors/aos.min.css',
    'public/assets/front/css/vendors/animate.min.css',
    'public/assets/front/css/vendors/datatables.min.css',
    'public/assets/css/toastr.min.css',
    'public/assets/css/select2.min.css',
    'public/assets/css/slick.css',
    'public/assets/css/jquery-ui.min.css',
    'public/assets/css/floating-whatsapp.css',
    'public/assets/css/main-default-front.css',
    'public/assets/front/css/style.css',
    'public/assets/css/summernote-content.css'
], 'public/assets/dist/css/frontend-v3.min.css');

// Frontend CSS Bundle - Theme V2
mix.combine([
    'public/assets/front/css/vendors/bootstrap.min.css',
    'public/assets/front/fonts/fontawesome/css/all.min.css',
    'public/assets/front/fonts/icomoon/style.css',
    'public/assets/front/css/vendors/magnific-popup.min.css',
    'public/assets/front/css/vendors/swiper-bundle.min.css',
    'public/assets/front/css/vendors/nice-select.css',
    'public/assets/front/css/vendors/aos.min.css',
    'public/assets/front/css/vendors/animate.min.css',
    'public/assets/css/main-default-front.css',
    'public/assets/front/css/style.css'
], 'public/assets/dist/css/frontend-v2.min.css');

// Frontend CSS Bundle - Theme V1
mix.combine([
    'public/assets/front/css/vendors/bootstrap.min.css',
    'public/assets/front/fonts/fontawesome/css/all.min.css',
    'public/assets/front/fonts/icomoon/style.css',
    'public/assets/front/css/vendors/magnific-popup.min.css',
    'public/assets/front/css/vendors/swiper-bundle.min.css',
    'public/assets/front/css/vendors/nice-select.css',
    'public/assets/css/main-default-front.css',
    'public/assets/front/css/style.css'
], 'public/assets/dist/css/frontend-v1.min.css');

// Inner pages CSS Bundle
mix.combine([
    'public/assets/front/css/default.css',
    'public/assets/front/css/innerpages.css',
    'public/assets/front/css/innerpages-responsive.css'
], 'public/assets/dist/css/innerpages.min.css');

// RTL CSS Bundle
mix.combine([
    'public/assets/front/css/rtl.css',
    'public/assets/front/css/innerpages-rtl.css'
], 'public/assets/dist/css/rtl.min.css');

// Frontend JavaScript Bundle
mix.combine([
    'public/assets/js/jquery-3.7.1.min.js',
    'public/assets/js/bootstrap.min.js',
    'public/assets/js/popper.min.js',
    'public/assets/front/js/vendors/bootstrap.bundle.min.js',
    'public/assets/front/js/vendors/magnific-popup.min.js',
    'public/assets/front/js/vendors/swiper-bundle.min.js',
    'public/assets/front/js/vendors/nice-select.min.js',
    'public/assets/front/js/vendors/aos.min.js',
    'public/assets/js/toastr.min.js',
    'public/assets/js/select2.min.js',
    'public/assets/js/slick.min.js',
    'public/assets/js/jquery-ui.min.js',
    'public/assets/js/floating-whatsapp.js'
], 'public/assets/dist/js/frontend.min.js');

// Critical CSS extraction for above-the-fold content
mix.extract([
    'bootstrap',
    'jquery'
]);

// Enable versioning for cache busting
mix.version();

// Production optimizations
if (mix.inProduction()) {
    mix.options({
        terser: {
            terserOptions: {
                compress: {
                    drop_console: true,
                }
            }
        }
    });
}

// Enable source maps for development
if (!mix.inProduction()) {
    mix.sourceMaps();
}
