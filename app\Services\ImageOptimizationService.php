<?php

namespace App\Services;

use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class ImageOptimizationService
{
    const CACHE_TTL = 86400; // 24 hours
    const WEBP_QUALITY = 80;
    const JPEG_QUALITY = 85;

    /**
     * Generate responsive image sizes
     */
    public function generateResponsiveImages($imagePath, $sizes = [])
    {
        $defaultSizes = [
            'thumbnail' => ['width' => 300, 'height' => 200],
            'medium' => ['width' => 600, 'height' => 400],
            'large' => ['width' => 1200, 'height' => 800],
        ];

        $sizes = array_merge($defaultSizes, $sizes);
        $generatedImages = [];

        foreach ($sizes as $sizeName => $dimensions) {
            $generatedImages[$sizeName] = $this->resizeImage(
                $imagePath, 
                $dimensions['width'], 
                $dimensions['height'],
                $sizeName
            );
        }

        return $generatedImages;
    }

    /**
     * Resize and optimize image
     */
    public function resizeImage($imagePath, $width, $height, $suffix = '')
    {
        $cacheKey = 'optimized_image_' . md5($imagePath . $width . $height . $suffix);
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($imagePath, $width, $height, $suffix) {
            $fullPath = public_path('assets/img/' . $imagePath);
            
            if (!file_exists($fullPath)) {
                return null;
            }

            $pathInfo = pathinfo($imagePath);
            $filename = $pathInfo['filename'];
            $extension = $pathInfo['extension'];
            $directory = $pathInfo['dirname'] !== '.' ? $pathInfo['dirname'] . '/' : '';

            // Generate optimized filename
            $optimizedFilename = $filename . '_' . $width . 'x' . $height;
            if ($suffix) {
                $optimizedFilename .= '_' . $suffix;
            }

            // WebP version
            $webpPath = 'assets/img/optimized/' . $directory . $optimizedFilename . '.webp';
            $webpFullPath = public_path($webpPath);

            // JPEG/PNG fallback
            $fallbackPath = 'assets/img/optimized/' . $directory . $optimizedFilename . '.' . $extension;
            $fallbackFullPath = public_path($fallbackPath);

            // Create optimized directory if it doesn't exist
            $optimizedDir = dirname($webpFullPath);
            if (!is_dir($optimizedDir)) {
                mkdir($optimizedDir, 0755, true);
            }

            // Generate WebP version
            if (!file_exists($webpFullPath)) {
                $image = Image::make($fullPath);
                $image->resize($width, $height, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
                $image->encode('webp', self::WEBP_QUALITY);
                $image->save($webpFullPath);
            }

            // Generate fallback version
            if (!file_exists($fallbackFullPath)) {
                $image = Image::make($fullPath);
                $image->resize($width, $height, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
                
                if (in_array(strtolower($extension), ['jpg', 'jpeg'])) {
                    $image->encode('jpg', self::JPEG_QUALITY);
                } else {
                    $image->encode($extension);
                }
                
                $image->save($fallbackFullPath);
            }

            return [
                'webp' => $webpPath,
                'fallback' => $fallbackPath,
                'width' => $width,
                'height' => $height
            ];
        });
    }

    /**
     * Generate picture element HTML for responsive images
     */
    public function generatePictureElement($imagePath, $alt = '', $class = '', $sizes = [])
    {
        $responsiveImages = $this->generateResponsiveImages($imagePath, $sizes);
        
        if (empty($responsiveImages)) {
            return '<img src="' . asset('assets/img/' . $imagePath) . '" alt="' . $alt . '" class="' . $class . '">';
        }

        $html = '<picture>';
        
        // WebP sources
        foreach ($responsiveImages as $sizeName => $imageData) {
            if ($imageData && isset($imageData['webp'])) {
                $mediaQuery = $this->getMediaQuery($sizeName);
                $html .= '<source srcset="' . asset($imageData['webp']) . '" type="image/webp"' . $mediaQuery . '>';
            }
        }

        // Fallback sources
        foreach ($responsiveImages as $sizeName => $imageData) {
            if ($imageData && isset($imageData['fallback'])) {
                $mediaQuery = $this->getMediaQuery($sizeName);
                $html .= '<source srcset="' . asset($imageData['fallback']) . '"' . $mediaQuery . '>';
            }
        }

        // Default img tag
        $defaultImage = $responsiveImages['medium'] ?? $responsiveImages['large'] ?? reset($responsiveImages);
        $defaultSrc = $defaultImage ? asset($defaultImage['fallback']) : asset('assets/img/' . $imagePath);
        
        $html .= '<img src="' . $defaultSrc . '" alt="' . $alt . '" class="' . $class . '" loading="lazy">';
        $html .= '</picture>';

        return $html;
    }

    /**
     * Get media query for responsive breakpoints
     */
    private function getMediaQuery($sizeName)
    {
        $mediaQueries = [
            'thumbnail' => ' media="(max-width: 576px)"',
            'medium' => ' media="(max-width: 992px)"',
            'large' => ' media="(min-width: 993px)"',
        ];

        return $mediaQueries[$sizeName] ?? '';
    }

    /**
     * Clear optimized image cache
     */
    public function clearImageCache($imagePath = null)
    {
        if ($imagePath) {
            $pattern = 'optimized_image_' . md5($imagePath) . '*';
            Cache::flush(); // Simple approach - in production, use more specific cache clearing
        } else {
            // Clear all optimized image caches
            Cache::flush();
        }
    }

    /**
     * Get optimized image URL with lazy loading
     */
    public function getOptimizedImageUrl($imagePath, $width = 600, $height = 400)
    {
        $optimizedImage = $this->resizeImage($imagePath, $width, $height);
        
        if ($optimizedImage && isset($optimizedImage['webp'])) {
            return asset($optimizedImage['webp']);
        }
        
        return asset('assets/img/' . $imagePath);
    }
}
