<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Critical indexes for home page performance
        
        // Services table indexes
        Schema::table('services', function (Blueprint $table) {
            $table->index(['service_status', 'is_featured'], 'idx_services_status_featured');
            $table->index(['seller_id', 'service_status'], 'idx_services_seller_status');
        });

        // Memberships table indexes
        Schema::table('memberships', function (Blueprint $table) {
            $table->index(['seller_id', 'status', 'start_date', 'expire_date'], 'idx_memberships_seller_active');
            $table->index(['status', 'start_date', 'expire_date'], 'idx_memberships_active_dates');
        });

        // Service contents table indexes
        Schema::table('service_contents', function (Blueprint $table) {
            $table->index(['service_category_id', 'language_id'], 'idx_service_contents_category_lang');
            $table->index(['service_id', 'language_id'], 'idx_service_contents_service_lang');
        });

        // Service categories table indexes
        Schema::table('service_categories', function (Blueprint $table) {
            $table->index(['language_id', 'status', 'is_featured'], 'idx_categories_lang_status_featured');
            $table->index(['status', 'serial_number'], 'idx_categories_status_serial');
        });

        // Sellers table indexes
        Schema::table('sellers', function (Blueprint $table) {
            $table->index('status', 'idx_sellers_status');
        });

        // Languages table indexes
        Schema::table('languages', function (Blueprint $table) {
            $table->index('is_default', 'idx_languages_default');
            $table->index('code', 'idx_languages_code');
        });

        // Posts table indexes for blog section
        Schema::table('posts', function (Blueprint $table) {
            $table->index('created_at', 'idx_posts_created_at');
        });

        // Post informations table indexes
        Schema::table('post_informations', function (Blueprint $table) {
            $table->index(['language_id', 'blog_category_id'], 'idx_post_info_lang_category');
        });

        // Features table indexes
        Schema::table('features', function (Blueprint $table) {
            $table->index('language_id', 'idx_features_language');
        });

        // Testimonials table indexes
        Schema::table('testimonials', function (Blueprint $table) {
            $table->index('language_id', 'idx_testimonials_language');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropIndex('idx_services_status_featured');
            $table->dropIndex('idx_services_seller_status');
        });

        Schema::table('memberships', function (Blueprint $table) {
            $table->dropIndex('idx_memberships_seller_active');
            $table->dropIndex('idx_memberships_active_dates');
        });

        Schema::table('service_contents', function (Blueprint $table) {
            $table->dropIndex('idx_service_contents_category_lang');
            $table->dropIndex('idx_service_contents_service_lang');
        });

        Schema::table('service_categories', function (Blueprint $table) {
            $table->dropIndex('idx_categories_lang_status_featured');
            $table->dropIndex('idx_categories_status_serial');
        });

        Schema::table('sellers', function (Blueprint $table) {
            $table->dropIndex('idx_sellers_status');
        });

        Schema::table('languages', function (Blueprint $table) {
            $table->dropIndex('idx_languages_default');
            $table->dropIndex('idx_languages_code');
        });

        Schema::table('posts', function (Blueprint $table) {
            $table->dropIndex('idx_posts_created_at');
        });

        Schema::table('post_informations', function (Blueprint $table) {
            $table->dropIndex('idx_post_info_lang_category');
        });

        Schema::table('features', function (Blueprint $table) {
            $table->dropIndex('idx_features_language');
        });

        Schema::table('testimonials', function (Blueprint $table) {
            $table->dropIndex('idx_testimonials_language');
        });
    }
};
