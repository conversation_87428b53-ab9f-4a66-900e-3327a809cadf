<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class PerformanceMonitor
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        // Enable query logging
        DB::enableQueryLog();
        
        $response = $next($request);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        $memoryUsage = ($endMemory - $startMemory) / 1024 / 1024; // Convert to MB
        $queries = DB::getQueryLog();
        $queryCount = count($queries);
        
        // Calculate total query time
        $totalQueryTime = 0;
        foreach ($queries as $query) {
            $totalQueryTime += $query['time'];
        }
        
        // Performance metrics
        $metrics = [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'execution_time' => round($executionTime, 2),
            'memory_usage' => round($memoryUsage, 2),
            'query_count' => $queryCount,
            'total_query_time' => round($totalQueryTime, 2),
            'timestamp' => now()->toISOString(),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip()
        ];
        
        // Log slow requests (> 2 seconds)
        if ($executionTime > 2000) {
            Log::warning('Slow request detected', $metrics);
            
            // Log slow queries
            foreach ($queries as $query) {
                if ($query['time'] > 100) { // Queries taking more than 100ms
                    Log::warning('Slow query detected', [
                        'sql' => $query['query'],
                        'bindings' => $query['bindings'],
                        'time' => $query['time']
                    ]);
                }
            }
        }
        
        // Log excessive queries (N+1 problem indicator)
        if ($queryCount > 20) {
            Log::warning('Excessive queries detected', $metrics);
        }
        
        // Store metrics in cache for dashboard (optional)
        if (config('app.debug')) {
            $this->storeMetrics($metrics);
        }
        
        // Add performance headers for debugging
        if (config('app.debug')) {
            $response->headers->set('X-Execution-Time', $executionTime . 'ms');
            $response->headers->set('X-Memory-Usage', $memoryUsage . 'MB');
            $response->headers->set('X-Query-Count', $queryCount);
            $response->headers->set('X-Query-Time', $totalQueryTime . 'ms');
        }
        
        return $response;
    }
    
    /**
     * Store performance metrics for analysis
     */
    private function storeMetrics($metrics)
    {
        $cacheKey = 'performance_metrics_' . date('Y-m-d-H');
        $existingMetrics = Cache::get($cacheKey, []);
        $existingMetrics[] = $metrics;
        
        // Keep only last 100 requests per hour
        if (count($existingMetrics) > 100) {
            $existingMetrics = array_slice($existingMetrics, -100);
        }
        
        Cache::put($cacheKey, $existingMetrics, 3600); // Store for 1 hour
    }
    
    /**
     * Get performance metrics for analysis
     */
    public static function getMetrics($hours = 1)
    {
        $metrics = [];
        
        for ($i = 0; $i < $hours; $i++) {
            $cacheKey = 'performance_metrics_' . date('Y-m-d-H', strtotime("-{$i} hours"));
            $hourlyMetrics = Cache::get($cacheKey, []);
            $metrics = array_merge($metrics, $hourlyMetrics);
        }
        
        return $metrics;
    }
    
    /**
     * Get performance summary
     */
    public static function getPerformanceSummary($hours = 1)
    {
        $metrics = self::getMetrics($hours);
        
        if (empty($metrics)) {
            return null;
        }
        
        $executionTimes = array_column($metrics, 'execution_time');
        $memoryUsages = array_column($metrics, 'memory_usage');
        $queryCounts = array_column($metrics, 'query_count');
        $queryTimes = array_column($metrics, 'total_query_time');
        
        return [
            'total_requests' => count($metrics),
            'avg_execution_time' => round(array_sum($executionTimes) / count($executionTimes), 2),
            'max_execution_time' => max($executionTimes),
            'avg_memory_usage' => round(array_sum($memoryUsages) / count($memoryUsages), 2),
            'max_memory_usage' => max($memoryUsages),
            'avg_query_count' => round(array_sum($queryCounts) / count($queryCounts), 2),
            'max_query_count' => max($queryCounts),
            'avg_query_time' => round(array_sum($queryTimes) / count($queryTimes), 2),
            'max_query_time' => max($queryTimes),
            'slow_requests' => count(array_filter($executionTimes, function($time) {
                return $time > 2000;
            })),
            'excessive_query_requests' => count(array_filter($queryCounts, function($count) {
                return $count > 20;
            }))
        ];
    }
}
