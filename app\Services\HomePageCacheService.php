<?php

namespace App\Services;

use App\Models\BasicSettings\Basic;
use App\Models\BasicSettings\BasicExtends;
use App\Models\Blog\Post;
use App\Models\ClientService\ServiceContent;
use App\Models\HomePage\CtaSectionInfo;
use App\Models\HomePage\Partner;
use App\Models\HomePage\Section;
use App\Models\Language;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class HomePageCacheService
{
    const CACHE_TTL = 3600; // 1 hour
    const CACHE_PREFIX = 'homepage_';

    /**
     * Get cached home page data or generate and cache it
     */
    public function getHomePageData($language, $themeVersion)
    {
        $cacheKey = self::CACHE_PREFIX . $language->id . '_' . $themeVersion;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($language, $themeVersion) {
            return $this->generateHomePageData($language, $themeVersion);
        });
    }

    /**
     * Generate home page data
     */
    private function generateHomePageData($language, $themeVersion)
    {
        $queryResult = [];
        
        // Get section info
        $secInfo = Section::query()->first();
        $queryResult['secInfo'] = $secInfo;

        // Get basic settings in one query
        $basicSettings = Basic::query()->select(
            'hero_static_img', 
            'hero_video_url', 
            'hero_bg_img', 
            'about_section_image', 
            'about_section_video_link',
            'feature_bg_img',
            'cta_bg_img'
        )->first();

        // Hero section
        if ($themeVersion == 1 || $themeVersion == 2 || $themeVersion == 3) {
            $queryResult['heroImg'] = $basicSettings->hero_static_img;
            $queryResult['heroVideoUrl'] = $basicSettings->hero_video_url;
            $queryResult['heroBgImg'] = $basicSettings->hero_bg_img;
        }
        $queryResult['heroInfo'] = $language->heroStatic()->first();

        // Categories
        $queryResult['categories'] = $language->serviceCategory()
            ->where('status', 1)
            ->orderBy('serial_number', 'asc')
            ->limit(8)
            ->get();

        // About section
        if ($secInfo->about_section_status == 1) {
            $queryResult['aboutInfo'] = (object)[
                'about_section_image' => $basicSettings->about_section_image,
                'about_section_video_link' => $basicSettings->about_section_video_link
            ];
            $queryResult['aboutData'] = $language->aboutSection()->first();
        }

        // Section titles
        $queryResult['secTitle'] = $language->sectionTitle()->first();

        // Features
        if ($secInfo->features_section_status == 1) {
            $queryResult['featureBgImg'] = $basicSettings->feature_bg_img;
            $queryResult['allFeature'] = $language->feature()->orderByDesc('id')->get();
        }

        // Featured services (optimized)
        if ($secInfo->featured_services_section_status == 1) {
            $queryResult['featuredCategories'] = $this->getFeaturedServices($language);
        }

        // Testimonials
        $queryResult['testimonials'] = $language->testimonial()->orderByDesc('id')->get();

        // Blog posts
        if ($secInfo->blog_section_status == 1) {
            $queryResult['posts'] = $this->getBlogPosts($language);
        }

        // Partners
        if ($secInfo->partners_section_status == 1) {
            $queryResult['partners'] = Partner::query()->orderByDesc('id')->get();
        }

        // CTA section
        if ($secInfo->cta_section_status == 1) {
            $queryResult['ctaSectionInfo'] = CtaSectionInfo::where('language_id', $language->id)->first();
            $queryResult['ctaBgImg'] = $basicSettings->cta_bg_img;
        }

        // Basic extends
        $queryResult['BasicExtends'] = BasicExtends::where('language_id', $language->id)->first();

        return $queryResult;
    }

    /**
     * Get featured services with optimized query
     */
    private function getFeaturedServices($language)
    {
        return $language->serviceCategory()
            ->where('status', 1)
            ->where('is_featured', 'yes')
            ->with(['serviceContent' => function ($query) {
                $query->whereHas('service', function (Builder $subQuery) {
                    $subQuery->where('service_status', 1)
                        ->where('is_featured', 'yes')
                        ->whereExists(function ($membershipQuery) {
                            $membershipQuery->select(DB::raw(1))
                                ->from('memberships')
                                ->whereColumn('memberships.seller_id', 'services.seller_id')
                                ->where('memberships.status', 1)
                                ->where('memberships.start_date', '<=', Carbon::now()->format('Y-m-d'))
                                ->where('memberships.expire_date', '>=', Carbon::now()->format('Y-m-d'));
                        })
                        ->whereExists(function ($sellerQuery) {
                            $sellerQuery->select(DB::raw(1))
                                ->from('sellers')
                                ->whereColumn('sellers.id', 'services.seller_id')
                                ->where('sellers.status', 1);
                        });
                });
            }])
            ->orderBy('serial_number', 'asc')
            ->get();
    }

    /**
     * Get blog posts with optimized query
     */
    private function getBlogPosts($language)
    {
        return Post::query()
            ->join('post_informations', 'posts.id', '=', 'post_informations.post_id')
            ->join('blog_categories', 'blog_categories.id', '=', 'post_informations.blog_category_id')
            ->where('post_informations.language_id', '=', $language->id)
            ->select(
                'posts.id', 
                'posts.image', 
                'blog_categories.name as categoryName', 
                'blog_categories.slug as categorySlug', 
                'post_informations.title', 
                'post_informations.slug', 
                'post_informations.author', 
                'post_informations.content', 
                'posts.created_at'
            )
            ->orderBy('posts.created_at', 'desc')
            ->limit(3)
            ->get();
    }

    /**
     * Clear home page cache
     */
    public function clearCache($languageId = null)
    {
        if ($languageId) {
            for ($theme = 1; $theme <= 3; $theme++) {
                Cache::forget(self::CACHE_PREFIX . $languageId . '_' . $theme);
            }
        } else {
            // Clear all home page caches
            $languages = Language::all();
            foreach ($languages as $language) {
                for ($theme = 1; $theme <= 3; $theme++) {
                    Cache::forget(self::CACHE_PREFIX . $language->id . '_' . $theme);
                }
            }
        }
    }

    /**
     * Warm up cache for all languages and themes
     */
    public function warmUpCache()
    {
        $languages = Language::all();
        foreach ($languages as $language) {
            for ($theme = 1; $theme <= 3; $theme++) {
                $this->getHomePageData($language, $theme);
            }
        }
    }
}
